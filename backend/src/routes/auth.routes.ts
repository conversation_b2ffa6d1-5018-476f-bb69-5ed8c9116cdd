import { Router } from "express";
import {
  register,
  login,
  refreshToken,
  logout,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  verifyEmailOTP,
  resendEmailVerificationOTP,
  verifyPasswordResetOTP,
  resetPasswordWithOTP,
} from "../controllers/auth.controller";

const router = Router();

// Auth routes
router.post("/register", register as any);
router.post("/login", login as any);
router.post("/refresh-token", refreshToken as any);
router.post("/logout", logout as any);

// Email verification routes
router.post("/verify-email-otp", verifyEmailOTP as any);
router.post("/resend-email-verification", resendEmailVerificationOTP as any);
router.get("/verify-email/:token", verifyEmail as any);

// Password reset routes
router.post("/request-password-reset", requestPasswordReset as any);
router.post("/verify-password-reset-otp", verifyPasswordResetOTP as any);
router.post("/reset-password-with-otp", resetPasswordWithOTP as any);
router.post("/reset-password", resetPassword as any);

export default router;
