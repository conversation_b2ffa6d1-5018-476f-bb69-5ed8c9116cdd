import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { Strategy as AppleStrategy } from "passport-apple";
import { prisma } from "../index";
import { generateTokens } from "../utils/jwt";

// Define OAuth user interface for Passport
interface OAuthUser {
  id: string;
  name: string;
  email: string;
  role: string;
  stripeCustomerId?: string;
}

// OAuth Configuration
export const configureOAuth = () => {
  // Google OAuth Strategy
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID!,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
        callbackURL: `${process.env.BASE_URL}/auth/google/callback`,
        scope: ["profile", "email"],
      },
      async (accessToken, refreshToken, profile, done) => {
        try {
          const email = profile.emails?.[0]?.value;
          const googleId = profile.id;
          const firstName = profile.name?.givenName;
          const lastName = profile.name?.familyName;
          const avatarUrl = profile.photos?.[0]?.value;

          if (!email) {
            return done(new Error("No email found in Google profile"), false);
          }

          // Check if user exists with this Google ID
          let user = await prisma.user.findUnique({
            where: { googleId },
            include: {
              profile: true,
              credit: true,
            },
          });

          if (user) {
            // Update last login
            await prisma.user.update({
              where: { id: user.id },
              data: { lastLogin: new Date() },
            });

            const oauthUser: OAuthUser = {
              id: user.id,
              name: `${user.profile?.firstName || ""} ${user.profile?.lastName || ""}`.trim(),
              email: user.email,
              role: user.role,
              stripeCustomerId: user.stripeCustomerId || undefined,
            };

            return done(null, oauthUser);
          }

          // Check if user exists with this email (for account linking)
          const existingUser = await prisma.user.findUnique({
            where: { email },
            include: {
              profile: true,
              credit: true,
            },
          });

          if (existingUser) {
            // Link Google account to existing user
            const updatedUser = await prisma.user.update({
              where: { id: existingUser.id },
              data: {
                googleId,
                emailVerified: true, // Google accounts are pre-verified
                lastLogin: new Date(),
              },
              include: {
                profile: true,
                credit: true,
              },
            });

            const oauthUser: OAuthUser = {
              id: updatedUser.id,
              name: `${updatedUser.profile?.firstName || ""} ${updatedUser.profile?.lastName || ""}`.trim(),
              email: updatedUser.email,
              role: updatedUser.role,
              stripeCustomerId: updatedUser.stripeCustomerId || undefined,
            };

            return done(null, oauthUser);
          }

          // Create new user with Google OAuth
          const newUser = await prisma.user.create({
            data: {
              email,
              googleId,
              authProvider: "GOOGLE",
              emailVerified: true, // Google accounts are pre-verified
              lastLogin: new Date(),
              profile: {
                create: {
                  firstName: firstName || "",
                  lastName: lastName || "",
                  avatarUrl,
                },
              },
              credit: {
                create: {
                  balance: 20, // Welcome credits
                },
              },
            },
            include: {
              profile: true,
              credit: true,
            },
          });

          const oauthUser: OAuthUser = {
            id: newUser.id,
            name: `${newUser.profile?.firstName || ""} ${newUser.profile?.lastName || ""}`.trim(),
            email: newUser.email,
            role: newUser.role,
            stripeCustomerId: newUser.stripeCustomerId || undefined,
          };

          return done(null, oauthUser);
        } catch (error) {
          console.error("Google OAuth error:", error);
          return done(error, false);
        }
      }
    )
  );

  // Apple OAuth Strategy
  passport.use(
    new AppleStrategy(
      {
        clientID: process.env.APPLE_CLIENT_ID!,
        teamID: process.env.APPLE_TEAM_ID!,
        keyID: process.env.APPLE_KEY_ID!,
        privateKeyString: process.env.APPLE_PRIVATE_KEY!,
        callbackURL: `${process.env.BASE_URL}/auth/apple/callback`,
        scope: ["name", "email"],
      },
      async (accessToken, refreshToken, idToken, profile: any, done) => {
        try {
          const email = profile.email;
          const appleId = profile.id;
          const firstName = profile.name?.firstName;
          const lastName = profile.name?.lastName;

          if (!email) {
            return done(new Error("No email found in Apple profile"), false);
          }

          // Check if user exists with this Apple ID
          let user = await prisma.user.findUnique({
            where: { appleId },
            include: {
              profile: true,
              credit: true,
            },
          });

          if (user) {
            // Update last login
            await prisma.user.update({
              where: { id: user.id },
              data: { lastLogin: new Date() },
            });
            return done(null, user);
          }

          // Check if user exists with this email (for account linking)
          const existingUser = await prisma.user.findUnique({
            where: { email },
            include: {
              profile: true,
              credit: true,
            },
          });

          if (existingUser) {
            // Link Apple account to existing user
            const updatedUser = await prisma.user.update({
              where: { id: existingUser.id },
              data: {
                appleId,
                emailVerified: true, // Apple accounts are pre-verified
                lastLogin: new Date(),
              },
              include: {
                profile: true,
                credit: true,
              },
            });
            return done(null, updatedUser);
          }

          // Create new user with Apple OAuth
          const newUser = await prisma.user.create({
            data: {
              email,
              appleId,
              authProvider: "APPLE",
              emailVerified: true, // Apple accounts are pre-verified
              lastLogin: new Date(),
              profile: {
                create: {
                  firstName: firstName || "",
                  lastName: lastName || "",
                },
              },
              credit: {
                create: {
                  balance: 20, // Welcome credits
                },
              },
            },
            include: {
              profile: true,
              credit: true,
            },
          });

          return done(null, newUser);
        } catch (error) {
          console.error("Apple OAuth error:", error);
          return done(error, null);
        }
      }
    )
  );

  // Serialize user for session
  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  // Deserialize user from session
  passport.deserializeUser(async (id: string, done) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          profile: true,
          credit: true,
        },
      });
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  });
};

export default passport;
